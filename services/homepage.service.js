import API from './api';

/**
 * Fetches homepage categories data
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Array>} - Array of homepage categories data
 */
export const getHomepageCategories = async (headers = null) => {
  let data = [];
  try {
    const result = await API.get('/homepage/homepage-categories', {}, headers ? { headers } : null);
    if (result && result.data && result.data.data) {
      data = result.data.data;
    }
    return data;
  } catch (error) {
    console.error('Error fetching homepage categories:', error);
    return [];
  }
};

/**
 * Fetches banner slider data for the homepage hero section
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Object|null>} - Banner slider data object or null if none exists
 */
export const getBannerSliders = async (headers = null) => {
  try {
    const result = await API.get('/homepage/banner-sliders', {}, headers ? { headers } : null);
    if (result && result.data && result.data.data.length > 0) {
      // Return the first item from the data array
      return result.data.data[0];
    }
    return null;
  } catch (error) {
    console.error('Error fetching banner sliders:', error);
    return null;
  }
};

/**
 * Fetches coupon of the week data for the homepage
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Array>} - Array of coupon of the week data
 */
export const getCouponOfTheWeek = async (headers = null) => {
  let data = [];
  try {
    const result = await API.get('/homepage/coupon-of-the-weeks', {}, headers ? { headers } : null);
    if (result && result.data) {
      data = result.data?.data;
    }
    return data;
  } catch (error) {
    console.error('Error fetching coupon of the week:', error);
    return [];
  }
};

/**
 * Fetches popular brands data for the homepage
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Array>} - Array of popular brands data
 */
export const getPopularEntities = async (headers = null) => {
  let data = [];
  try {
    const result = await API.get('/homepage/popular-entities', {}, headers ? { headers } : null);
    if (result && result.data) {
      data = result?.data?.data[0];
      console.log('Popular Entities:', data);
    }
    return data;
  } catch (error) {
    console.error('Error fetching popular brands:', error);
    return [];
  }
};

/**
 * Fetches tested for you data for the homepage
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Array>} - Array of tested for you data
 */
export const getTestedForYou = async (headers = null) => {
  let data = [];
  try {
    const result = await API.get('/homepage/tested-for-you', {}, headers ? { headers } : null);
    if (result && result.data) {
      data = result.data.data;
    }
    return data;
  } catch (error) {
    console.error('Error fetching tested for you data:', error);
    return [];
  }
};

/**
 * Fetches text banner data for the homepage
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Object>} - Object of text banner data
 */
export const getTextBanner = async (headers = null) => {
  let data = [];
  try {
    const result = await API.get('/homepage/text-banner', {}, headers ? { headers } : null);
    if (result && result.data) {
      data = result.data.data[0];
    }
    return data;
  } catch (error) {
    console.error('Error fetching text banners:', error);
    return [];
  }
};

/**
 * Fetches all homepage data in a single object
 * @param {Object} headers - Optional headers for authenticated requests
 * @returns {Promise<Object>} - Object containing all homepage data
 */
export const getAllHomepageData = async (headers = null) => {
  try {
    const [bannerSliders, couponOfTheWeek, popularEntities, testedForYou, textBanner, homepageCategories] = await Promise.all([
      getBannerSliders(headers),
      getCouponOfTheWeek(headers),
      getPopularEntities(headers),
      getTestedForYou(headers),
      getTextBanner(headers),
      getHomepageCategories(headers)
    ]);

    return {
      bannerSliders,
      couponOfTheWeek,
      popularEntities,
      testedForYou,
      textBanner,
      homepageCategories
    };
  } catch (error) {
    console.error('Error fetching all homepage data:', error);
    return {
      bannerSliders: null,
      couponOfTheWeek: [],
      popularEntities: {},
      testedForYou: [],
      textBanner: {},
      homepageCategories: []
    };
  }
};
