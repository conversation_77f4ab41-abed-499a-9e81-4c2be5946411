@import '../../styles/sass/mixins';

// Remove all link styling from the Link component
.brandLink {
  text-decoration: none !important;
  color: inherit !important;

  &:hover {
    text-decoration: none !important;
    color: inherit !important;
  }

  &:focus {
    text-decoration: none !important;
    color: inherit !important;
    outline: none;
  }

  &:active {
    text-decoration: none !important;
    color: inherit !important;
  }

  &:visited {
    text-decoration: none !important;
    color: inherit !important;
  }
}

.cardContainer {
  border-radius: 15px;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  margin: 15px 0;
  text-align: center;
  min-height: 120px; // Ensure consistent height for desktop

  // Remove all link styling
  text-decoration: none !important;
  color: inherit !important;

  &:hover {
    text-decoration: none !important;
    color: inherit !important;
  }

  &:focus {
    text-decoration: none !important;
    color: inherit !important;
    outline: none;
  }

  &:active {
    text-decoration: none !important;
    color: inherit !important;
  }

  &:visited {
    text-decoration: none !important;
    color: inherit !important;
  }
}

.discountText {
  font-size: 30px;
  font-weight: bold;
  color: #000;
}

.textContainer {
  text-align: center;
}

.description {
  margin-top: 10px;
  font-size: 12px;
  font-weight: 500;
}

.divider {
  width: 2px;
  border: 1px solid #AFAFAF;
  height: 6rem;
  margin: auto;
}

.brandImage {
  width: 135px !important;
  height: 80px !important;
}

// Mobile-specific styles
.mobileLayout {
  text-align: center;
  padding: 10px 5px;
  min-height: 160px; // Consistent height for mobile cards (3 per row)
  justify-content: space-between;
}

.mobileImageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;

  .brandImage {
    width: 60px !important;
    height: 38px !important;
    object-fit: contain;
  }
}

.mobileDivider {
  width: 95%;
  height: 1px;
  background-color: #AFAFAF;
  margin: 8px auto;
}

.mobileTextContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex-grow: 1;
  justify-content: center;

  .discountText {
    font-size: 22px;
    font-weight: bold;
    color: #000;
    margin-bottom: 5px;
    line-height: 1.1;
  }

  .description {
    font-size: 10px;
    font-weight: 500;
    line-height: 1.2;
    margin-top: 0;
    text-align: center;
    max-width: 100px; // Smaller width for 3-column layout
    word-wrap: break-word;
  }
}